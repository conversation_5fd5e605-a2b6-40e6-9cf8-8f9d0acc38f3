import { PlatformType } from '@pa/shared/dist/src/core';
import { Inject, Injectable } from '@gulux/gulux';
import { MrType } from '@shared/bits/mrInfo';
import {
  CreateMrComponent,
  CreateMrData,
  CreateMrDependency,
  CreateMrHost,
  CreateMrRepoInfo,
  CreateMrReviewer,
  CustomFields,
  PublishType,
  Reviewer,
  ReviewerRoleType,
  ReviewerType,
} from '@shared/bits/createMrData';
import { Platform, Version } from '@shared/versionBot/version';
import { BmInfo, BmType } from '@shared/bits/bmInfo';
import { Component } from '@shared/bits/components';
import VersionModelService from './model/versionModel';
import BitsService from './third/bits';
import MrComponent from './mrComponent';

export type MrRepos = {
  projectId: number;
  sourcesBranch: string;
  targetBranch: string;
  isHost: boolean;
  platform: PlatformType;
};

@Injectable()
export default class BitsConfigV2Service {
  @Inject()
  private versionModelService: VersionModelService;

  @Inject()
  private bitsService: BitsService;

  @Inject()
  private mrComponentService: MrComponent;

  async buildMrConfig(buildPrams: {
    title: string;
    repos: MrRepos[];
    targetVersion: string;
    author: string;
    type: MrType;
    customFields: CustomFields;
    wip: boolean;
    review_start_type?: any;
    qa?: string;
    group_name?: string;
  }): Promise<CreateMrData> {
    const hosts = await this.buildMrHostConfig(buildPrams.repos);
    const multiDependencies = await this.buildMultiDependency(buildPrams.repos);
    const mrReviewers = await this.buildMrReviewers(
      buildPrams.repos,
      buildPrams.targetVersion,
      buildPrams.author,
      buildPrams.qa,
    );

    return {
      note: '',
      type: buildPrams.type,
      hosts,
      qa_settings: false,
      optimize: buildPrams.title,
      remove_source: true,
      skip_feature_check: true,
      wip: buildPrams.wip,
      custom_fields: buildPrams.customFields,
      mr_reviewers: mrReviewers,
      multiple_host: multiDependencies.length > 0,
      mr_dependencies: multiDependencies,
      review_start_type: buildPrams.review_start_type ? buildPrams.review_start_type : 0,
      group_name: buildPrams.group_name,
    };
  }

  private async buildMrReviewers(
    repos: MrRepos[],
    targetVersion: string,
    author: string,
    qa?: string,
  ): Promise<CreateMrReviewer[]> {
    const createMrReviewer: CreateMrReviewer[] = [];
    const reviewers: Reviewer[] = [];
    reviewers.push({
      name: author,
      role: ReviewerRoleType.USER_ADD_GLOBAL_RD,
    });
    if (qa) {
      reviewers.push({
        name: qa,
        role: ReviewerRoleType.USER_ADD_GLOBAL_QA,
      });
    }
    createMrReviewer.push({
      type: ReviewerType.global,
      reviewers,
    });
    const versionInfo = await this.versionModelService.findVersion({
      version: targetVersion,
    } as Version);
    if (versionInfo) {
      const allHosts = repos.filter(value => value.isHost);
      for (const host of allHosts) {
        const versionPlatform = versionInfo.platform.find((value: Platform) => value.type === host.platform);
        if (versionPlatform) {
          const platformBms = await this.bitsService.requestVersionMaster(
            versionPlatform.name,
            versionPlatform.customVersion ? versionPlatform.customVersion : versionInfo.version,
          );
          const reviewerList = platformBms
            .filter((value: BmInfo) => value.type === BmType.qa || value.type === BmType.rd)
            .map(
              (value: BmInfo) =>
                ({
                  name: value.email.substring(0, value.email.indexOf('@')),
                  role:
                    value.type === BmType.rd ? ReviewerRoleType.USER_ADD_RD_BM : ReviewerRoleType.USER_ADD_GLOBAL_QA,
                }) as Reviewer,
            );
          if (reviewerList.length > 0) {
            createMrReviewer.push({
              type: ReviewerType.group,
              group_name: versionPlatform.name,
              reviewers: reviewerList,
            });
          }
        }
      }
    }
    return createMrReviewer;
  }

  private async buildMultiDependency(repos: MrRepos[]): Promise<CreateMrDependency[]> {
    const allHostInfo = repos.filter(value => value.isHost);
    const allMultiRepo = repos.filter((value: MrRepos) => !value.isHost && value.platform === PlatformType.Multi);
    const mrDependencies: CreateMrDependency[] = [];
    for (const multiRepo of allMultiRepo) {
      const multiComponents: CreateMrComponent[] = [];
      for (const hostRepo of allHostInfo) {
        if (hostRepo.platform === PlatformType.Android) {
          const androidComponent = await this.mrComponentService.recommendComponent(
            {
              project_id: multiRepo.projectId,
              source_branch: multiRepo.sourcesBranch,
              target_branch: multiRepo.targetBranch,
            },
            hostRepo.projectId,
          );

          // 特殊处理 project_id=514925，只保留 retouchsdk_vega-prod 组件
          if (multiRepo.projectId === 514925 && androidComponent.repos?.length > 0) {
            const filteredRepos = androidComponent.repos.filter(repo => repo.name === 'retouchsdk_vega-prod');
            if (filteredRepos.length > 0) {
              const filteredComponent: CreateMrComponent = {
                host_project_id: androidComponent.host_project_id,
                repos: filteredRepos,
              };
              multiComponents.push(filteredComponent);
            }
          } else {
            multiComponents.push(androidComponent);
          }
        } else {
          const component = await this.bitsService.requestProjectRepo(
            `${multiRepo.projectId}`,
            `${hostRepo.projectId}`,
          );
          if (component) {
            if (multiRepo.projectId !== 540549) {
              const otherComponent = {
                host_project_id: hostRepo.projectId,
                repos: component.repos.map((value: Component) => ({
                  component_id: value.id,
                  name: value.repoName,
                  publish_type: PublishType.auto,
                  version: {
                    correct: true,
                  },
                })),
              };
              multiComponents.push(otherComponent);
              continue;
            }
            const mrSubRepos: CreateMrRepoInfo[] = [];
            for (const repo of component.repos) {
              const componentInfo = await this.bitsService.searchComponentInfoByIdWithCache(repo.id);
              if (componentInfo.publishType === PublishType.sem) {
                let version: string;
                if (componentInfo.rawData) {
                  version = componentInfo.rawData.version;
                } else {
                  const serviceData = await this.bitsService.searchComponentInfoById(repo.id);
                  version = serviceData.version;
                }
                mrSubRepos.push({
                  component_id: repo.id,
                  name: repo.repoName,
                  publish_type: PublishType.sem,
                  version: {
                    version_base: version,
                    correct: true,
                    custom_suffix: '',
                    suffix: 'formal',
                    upgrade_type: 'patch',
                  },
                });
              } else {
                mrSubRepos.push({
                  component_id: repo.id,
                  name: repo.repoName,
                  publish_type: PublishType.auto,
                  version: {
                    correct: true,
                  },
                });
              }
            }
            const otherComponent = {
              host_project_id: hostRepo.projectId,
              repos: mrSubRepos,
            };
            multiComponents.push(otherComponent);
          }
        }
      }
      mrDependencies.push({
        project_gitlab_id: multiRepo.projectId,
        source: multiRepo.sourcesBranch,
        target: multiRepo.targetBranch,
        flutter_module: false,
        components: multiComponents,
      });
    }
    return mrDependencies;
  }

  private async buildMrHostConfig(repos: MrRepos[]): Promise<CreateMrHost[]> {
    const host: CreateMrHost[] = [];
    const allHostInfo = repos.filter(value => value.isHost);
    for (const mrRepo of allHostInfo) {
      // Android的组件通过自有接口获取
      // 获取所有当前架构下的子仓
      const allChildrenRepos = repos.filter(value => !value.isHost && value.platform === mrRepo.platform);
      const mrDependencies: CreateMrDependency[] = [];
      for (const childRepo of allChildrenRepos) {
        if (mrRepo.platform === PlatformType.Android) {
          const androidComponent = await this.mrComponentService.recommendComponent(
            {
              project_id: childRepo.projectId,
              source_branch: childRepo.sourcesBranch,
              target_branch: childRepo.targetBranch,
            },
            mrRepo.projectId,
          );
          // console.log('!!!!!!!!!!!!!!!!!!!androidrecomend');
          // console.log(androidComponent);

          // 特殊处理 project_id=514925，只保留 retouchsdk_vega-prod 组件
          if (childRepo.projectId === 514925 && androidComponent.repos?.length > 0) {
            const filteredRepos = androidComponent.repos.filter(repo => repo.name === 'retouchsdk_vega-prod');
            if (filteredRepos.length > 0) {
              const filteredComponent: CreateMrComponent = {
                host_project_id: androidComponent.host_project_id,
                repos: filteredRepos,
              };
              mrDependencies.push({
                project_gitlab_id: childRepo.projectId,
                source: childRepo.sourcesBranch,
                target: childRepo.targetBranch,
                flutter_module: false,
                components: [filteredComponent],
              });
            }
          } else if (androidComponent.repos?.length > 0) {
            // console.log('enter');
            mrDependencies.push({
              project_gitlab_id: childRepo.projectId,
              source: childRepo.sourcesBranch,
              target: childRepo.targetBranch,
              flutter_module: false,
              components: [androidComponent],
            });
          }
        } else {
          const component = await this.bitsService.requestProjectRepo(`${childRepo.projectId}`, `${mrRepo.projectId}`);

          if (component) {
            const mrSubRepos: CreateMrRepoInfo[] = [];
            for (const repo of component.repos) {
              const componentInfo = await this.bitsService.searchComponentInfoByIdWithCache(repo.id);
              if (componentInfo.publishType === PublishType.sem) {
                let version: string;
                if (componentInfo.rawData) {
                  version = componentInfo.rawData.version;
                } else {
                  const serviceData = await this.bitsService.searchComponentInfoById(repo.id);
                  version = serviceData.version;
                }
                mrSubRepos.push({
                  component_id: repo.id,
                  name: repo.repoName,
                  publish_type: PublishType.sem,
                  version: {
                    version_base: version,
                    correct: true,
                    custom_suffix: '',
                    suffix: 'formal',
                    upgrade_type: 'patch',
                  },
                });
              } else {
                mrSubRepos.push({
                  component_id: repo.id,
                  name: repo.repoName,
                  publish_type: PublishType.auto,
                  version: {
                    correct: true,
                  },
                });
              }
            }
            mrDependencies.push({
              project_gitlab_id: childRepo.projectId,
              source: childRepo.sourcesBranch,
              target: childRepo.targetBranch,
              flutter_module: false,
              components: [
                {
                  host_project_id: mrRepo.projectId,
                  repos: mrSubRepos,
                },
              ],
            });
          }
        }
      }
      host.push({
        project_gitlab_id: mrRepo.projectId,
        flutter_module: false,
        source: mrRepo.sourcesBranch,
        target: mrRepo.targetBranch,
        components: [],
        mr_dependencies: mrDependencies,
        version_dependencies: [],
      });
    }
    return host;
  }
}
