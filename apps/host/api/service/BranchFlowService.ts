import { BuildMasterInfo } from '@shared/process/versionProcess';
import { NetworkCode, NetworkResult, PlatformType, User } from '@pa/shared/dist/src/core';
import repos from '@shared/gitlab/repos';
import { nanoid } from 'nanoid';
import BitsConfigV2Service, { MrRepos } from './bitsConfigV2';
import {
  ConflictBlock,
  ConflictedType,
  ConflictFile,
  ConflictLineInfo,
  MrInfo,
  MrState,
  MrType,
} from '@shared/bits/mrInfo';
import { Card, CardTemplate, MessageType } from '@pa/shared/dist/src/lark/larkCard';
import { RedisClient } from '@gulux/plugin-redis/lib';
import { Inject, Injectable } from '@gulux/gulux';
import BitsService from './third/bits';
import GitLabService from './third/gitlab';
import { ProjectInfo } from '@shared/gitlab';
import VersionModelService from './model/versionModel';
import { BytedLogger } from '@gulux/gulux/byted-logger';
import LarkCardService from './larkCard';
import LarkService from '@pa/backend/dist/src/third/lark';
import { UserData, UserIdType } from '@pa/shared/dist/src/lark/userInfo';
import _, { sample, zip } from 'lodash';
import { CreateMrResult } from '@shared/bits/createMrData';
import { BmInfo, BmType } from '@shared/bits/bmInfo';
import { MrPipelineInfo } from '@shared/bits/mrPipelineInfo';
import axios from 'axios';
import VersionUtilService from './utils/VersionUtilService';
import versionUtils, { lv2PCVersion, version2VersionCode } from '../utils/versionUtils';
import { CreateChatArgs } from '@pa/shared/dist/src/lark/createChat';
import { Version } from '@shared/versionBot/version';
import { useInject } from '@edenx/runtime/bff';
import { CodeProcessor } from './codeProcess/codeProcessor';
import {
  createLVAndroidResolveConflictsV2,
  createLVIOSResolveConflicts,
  createLVPCResolveConflicts,
} from './codeProcess/codeProcessBuilder';
import { VersionProcessInfoService } from './releasePlatform/versionProcessInfoService';
import { teaCollect, TeaEvent } from '../tea';
import OnCallService from './third/oncall';
import { isDevelopBranch } from '@pa/backend/dist/src/utils/branch';
import VersionProcessInfoDao from './dao/releasePlatform/VersionProcessInfoDao';
import { AppSettingId, isPlatformApp } from '@pa/shared/dist/src/appSettings/appSettings';

interface MRBranchInfo {
  projectInfo: ProjectInfo;
  origin: string;
  target: string;
  platform: PlatformType;
  userName: string;
}

interface MultiMRBranchInfo {
  projectInfo: ProjectInfo;
  origin: string;
  target: string;
  platform: PlatformType;
  userName: string;
}

interface VersionBranch {
  branch: string;
  versionCode: number;
  bmUserName: string;
}

interface FlowInfo {
  source: string;
  target: string;
  timestamp: number;
  userInfo: User;
  mrUrl: string;
  mrTitle: string;
  progress: number;
  pipeline: MrPipelineInfo[];
}

type BranchFlowProgress = string[];

// redis key
const KEY_BRANCH_FLOW = 'BRANCH_FLOW';
// bm 回流群
const BM_CHAT_ID = 'oc_17ec09a3ab1d70906b2a9e6dfee6bba2';

const CONFLICT_IGNORE_FILE = ['dependency-lock.json', 'gradle.properties', 'Podfile.seer', 'Podfile.CapCut.seer'];

const Conflict_IGNORE_AUTHOR = ['BitsAdmin', 'ci_paper_airplane', 'root'];

@Injectable()
export default class BranchFlowService {
  @Inject()
  private versionProcessDao: VersionProcessInfoDao;
  @Inject()
  private bitsService: BitsService;
  @Inject()
  private gitlabService: GitLabService;
  @Inject()
  private versionModelService: VersionModelService;
  @Inject()
  private logger: BytedLogger;
  @Inject()
  private larkCardService: LarkCardService;
  @Inject()
  private bitsConfigV2Service: BitsConfigV2Service;
  @Inject()
  private larkService: LarkService;
  @Inject()
  private redisClient: RedisClient;
  @Inject()
  private versionUtil: VersionUtilService;
  @Inject()
  private codeProcessor: CodeProcessor;
  @Inject()
  private oncall: OnCallService;
  @Inject()
  private versionProcessInfoDao: VersionProcessInfoDao;

  TAG = 'BranchFlowService';

  constructor() {
    this.initialize();
  }

  private async initialize() {}

  private formatVersionCode(versionCode: number): string | null {
    const versionCodeStr = versionCode.toString();
    // 确定版本号长度至少为4位，例如1190应该转换为11.9.0
    if (versionCodeStr.length === 4) {
      return `${versionCodeStr.substring(0, 2)}.${versionCodeStr.substring(2, 3)}.${versionCodeStr.substring(3)}`;
    }
    return null;
  }

  async test_checkBranchFlow() {
    // const currentSeconds = Temporal.Now.zonedDateTimeISO(commonUtils.defaultTimeZone).epochSeconds;
    // const onWeek = 7 * 24 * 60 * 60;
    const versionModel = useInject(VersionProcessInfoService);
    const versionInfo = await versionModel.getCurrentVersionProcessInfo(2020092383, '7.2.0');
    if (!versionInfo) {
      return;
    }
    // const result = await useInject(MrMergedPackageServer).handler(
    //   '{"username": "ci_cony", "timestamp": 1737894575716, "group_name": "LV-iOS", "state": true, "mr_id": 7141467, "project_id": 39995}',
    // );
    // this.logger.info(`${this.TAG} result: ${JSON.stringify(result)}`);
    // const userStoryStage = versionInfo.version_stages.find(value => value.stage_name === 'user_story');
    // if (!userStoryStage) {
    //   return;
    // }
    // const action = useInject(LvUserStoryTestNotifyAction);
    // const result2 = await action.canExecute(versionInfo, userStoryStage, {
    //   action_type: 21,
    //   app_id: 177501,
    //   last_trigger_time: 0,
    //   stage: 'user_story',
    //   trigger_count: 0,
    //   version: '15.2.0',
    // });
    // const result = await useInject(MeegoService).businessAllInfo('faceu');
    // const result = await versionModel.updateVersionProcessInfo(versionInfo);
    // const adrGray = useInject(AdrGrayStageService);
    // const result2 = await adrGray.adrGrayReleaseMore("12.7.0", PlatformType.Android, LVProductType.cc, '12700300', '10%');
    // const card = useInject(VersionReleaseCardService);
    // const stage = currentStage(versionInfo);
    // if (!stage) {
    //   return;
    // }
    // const bc = await card.buildStageStartCard(versionInfo, stage.currentStage!);
    // const result = await this.larkService.sendCardMessage(UserIdType.chatId, 'oc_568745868df8983bf70f7f453a8e4924', bc)
    // const result = await this.larkService.chatId2OpenChatId('7406150885620908036');
    // versionInfo.bmReleaseGroupChatId = 'oc_5c6576269441f3797806a7a0e6f333c4';
    // const result = await versionModel.updateVersionProcessInfo(versionInfo);
    // await versionModel.createNewVersionProcessInfo();
    // const nextEvent: DataItem[] = await this.bitsService.calendarWorkSpaceEventNext(
    //   '封版',
    //   currentSeconds,
    //   30,
    // );
    // const reportService = useInject(GenerateReportService);
    // const report = await reportService.generateBusReport('14.2.0');
    // const report = await reportService.generateChecklistReport('14.2.0', 'systemTest', 177501);
    // this.logger.info(`${this.TAG} report: ${JSON.stringify(result)}`);
    // await useInject(PeopleMapModelService).quickCreatePCDirection('剪映专业版-工具', 'lixingpeng', 'zhujianjun');
    // await useInject(PeopleMapModelService).quickCreatePCDirection('剪映专业版-资源素材', 'shijiancheng', 'yecichang');
    // await useInject(PeopleMapModelService).quickCreatePCDirection(
    //   '剪映专业版-自媒体&营销',
    //   'shijiancheng',
    //   'yecichang',
    // );
    // await useInject(PeopleMapModelService).quickCreatePCDirection('剪映专业版-生态', 'lixingpeng', 'tanzhiyuan');
    // await useInject(PeopleMapModelService).quickCreatePCDirection('剪映专业版-主框架', 'lixingpeng', 'tanzhiyuan');
    // await useInject(PeopleMapModelService).quickCreatePCDirection('剪映专业版-商业化', 'lixingpeng', 'tanzhiyuan');
    // await useInject(PeopleMapModelService).quickCreatePCDirection(
    //   '剪映专业版-中间层（Lyra等）',
    //   'lixingpeng',
    //   'gexianglin',
    // );
    // await useInject(PeopleMapModelService).quickCreatePCDirection(
    //   '剪映专业版-多媒体-VESDK',
    //   'jipu.xiong',
    //   'gaochao.01',
    // );
    // await useInject(PeopleMapModelService).quickCreatePCDirection(
    //   '剪映专业版-多媒体-EffectSDK',
    //   'chengxiwen',
    //   'zhangshiyang',
    // );
    // await useInject(PeopleMapModelService).quickCreatePCDirection(
    //   '剪映专业版-智能创作-AI平台Bach/Lens',
    //   'yangfenghai',
    //   'guanyouguo',
    // );
    // await useInject(PeopleMapModelService).quickCreatePCDirection(
    //   '剪映专业版-智能创作-AI平台bytenn',
    //   'chengshu.2019',
    //   'wangshuai.hust',
    // );
    // await useInject(PeopleMapModelService).quickCreatePCDirection(
    //   '剪映专业版-智能创作-其他',
    //   'yangfenghai',
    //   'yangfenghai',
    // );
    // await useInject(PeopleMapModelService).quickCreatePCDirection('剪映专业版-安全&合规', 'tanzhiyuan', 'hurenhe');
    // await useInject(PeopleMapModelService).quickCreatePCDirection('剪映专业版-其他', 'lixingpeng', 'zhujianjun');
    // const firstConfig: QATestConfig = {
    //   app_id: 251501,
    //   poc_list: [
    //     {
    //       key: 'test_1',
    //       business_name: '测试配置',
    //       poc: {
    //         name: '郑柏伦',
    //         email: '<EMAIL>',
    //         user_id: 'egd2ec9b',
    //         open_id: 'ou_0e432eeab1b6de97ef9c7a06c9d5290b',
    //       } as User,
    //     } as PocConfig,
    //   ],
    // } as QATestConfig;
    // await useInject(QATestConfigDao).create(firstConfig);
    // const checklistTest = useInject(VersionStageCheckListService);
    // const result = await checklistTest.getVersionStageCheckList(
    //   { app_id: 177501, version: '14.5.0', stage: 'testflight_pre_check' },
    //   undefined,
    //   true,
    // );
    // this.logger.info(`${this.TAG} result: ${JSON.stringify(result)}`);
    // const checklist = await checklistTest.findBlockedOrExemptItems(177501, '14.0.0', 'testFlight', CheckItemStatus.Blocked)
    // const mrId = 6671406;
    // const mrInfo: MrInfo | undefined = await this.bitsService.getMrInfo({
    //   mrId,
    // });
    // if (mrInfo) {
    //   this.larkCardService
    //     .buildMrConflictInformCard(
    //       mrId,
    //       mrInfo?.from_branch,
    //       mrInfo?.target_branch,
    //       [PlatformType.PC],
    //       mrInfo,
    //       undefined,
    //       'linyang.yl',
    //     )
    //     .then(async (card: Card) => {
    //       await this.larkService.sendMessage(
    //         UserIdType.chatId,
    //         'oc_129335258d1fbab833e9d63db112113e',
    //         JSON.stringify(card),
    //         MessageType.interactive,
    //       );
    //     });
    // }
    // const versionInfos = await this.versionProcessDao.queryAutoFlowVersionInfo();
    // const lvVersions = versionInfos.filter(value => value.product === LVProductType.lv);
    // const ccVersion = versionInfos.filter(value => value.product === LVProductType.cc);
    // this.logger.info(`lvVersions: ${JSON.stringify(lvVersions)}`);
    // const androidLvBranches = lvVersions
    //   .filter(value => value.platform === PlatformType.Android && value.bmInfo !== undefined)
    //   .map(value => ({
    //     branch: `release/${value.version}`,
    //     versionCode: value.versionNum,
    //     bmUserName: this.getBM(value.bmInfo),
    //   }));
    // this.logger.info(`${this.TAG} android lv: ${JSON.stringify(androidLvBranches)}`);
    // const androidCcBranches = ccVersion
    //   .filter(value => value.platform === PlatformType.Android && value.bmInfo !== undefined)
    //   .map(value => ({
    //     branch: `overseas/release/${value.version}`,
    //     versionCode: value.versionNum,
    //     bmUserName: this.getBM(value.bmInfo),
    //   }));
    // this.logger.info(`${this.TAG} android cc: ${JSON.stringify(androidCcBranches)}`);
    // const iosLvBranches = lvVersions
    //   .filter(value => value.platform === PlatformType.iOS && value.bmInfo !== undefined)
    //   .map(value => ({
    //     branch: `release/${value.version}`,
    //     versionCode: value.versionNum,
    //     bmUserName: this.getBM(value.bmInfo),
    //   }));
    // this.logger.info(`${this.TAG} ios lv: ${JSON.stringify(iosLvBranches)}`);
    // const iosCcBranches = ccVersion
    //   .filter(value => value.platform === PlatformType.iOS && value.bmInfo !== undefined)
    //   .map(value => ({
    //     branch: `overseas/release/${value.version}`,
    //     versionCode: value.versionNum,
    //     bmUserName: this.getBM(value.bmInfo),
    //   }));
    // this.logger.info(`${this.TAG} ios cc: ${JSON.stringify(iosCcBranches)}`);
    // return iosLvBranches;
  }

  // 定时触发 30 分钟一次
  async checkBranchFlow() {
    // await this.redisClient.hdel(KEY_BRANCH_FLOW, 'Multi-release/15.3.0-develop-zhengbolun.patlon');
    const lviOSVersionInfos = await this.versionProcessInfoDao.findOnProgressVersions(AppSettingId.LV_IOS);
    if (!lviOSVersionInfos) {
      this.logger.error(`${this.TAG} lviOSVersionInfos is empty`);
      return;
    }
    const lvAdrVerisonInfos = await this.versionProcessInfoDao.findOnProgressVersions(AppSettingId.LV_ANDROID);
    if (!lvAdrVerisonInfos) {
      this.logger.error(`${this.TAG} lvAdrVerisonInfos is empty`);
      return;
    }
    const cciOSVersionInfos = await this.versionProcessInfoDao.findOnProgressVersions(AppSettingId.CC_IOS);
    if (!cciOSVersionInfos) {
      this.logger.error(`${this.TAG} cciOSVersionInfos is empty`);
      return;
    }
    const ccAdrVersionInfos = await this.versionProcessInfoDao.findOnProgressVersions(AppSettingId.CC_ANDROID);
    if (!ccAdrVersionInfos) {
      this.logger.error(`${this.TAG} ccAdrVersionInfos is empty`);
      return;
    }
    let androidLvBranches = lvAdrVerisonInfos
      .filter(value => value.bmInfo !== undefined)
      .map(value => ({
        branch: `release/${value.version}`,
        versionCode: Number(version2VersionCode(value.version)),
        bmUserName: this.getBM(value.bmInfo, PlatformType.Android),
      }));
    this.logger.info(`${this.TAG} android lv: ${JSON.stringify(androidLvBranches)}`);
    let androidCcBranches = ccAdrVersionInfos
      .filter(value => value.bmInfo !== undefined)
      .map(value => ({
        branch: `overseas/release/${value.version}`,
        versionCode: Number(version2VersionCode(value.version)),
        bmUserName: this.getBM(value.bmInfo, PlatformType.Android),
      }));
    this.logger.info(`${this.TAG} android cc: ${JSON.stringify(androidCcBranches)}`);
    let iosLvBranches = lviOSVersionInfos
      .filter(value => value.bmInfo !== undefined)
      .map(value => ({
        branch: `release/${value.version}`,
        versionCode: Number(version2VersionCode(value.version)),
        bmUserName: this.getBM(value.bmInfo, PlatformType.iOS),
      }));
    this.logger.info(`${this.TAG} ios lv: ${JSON.stringify(iosLvBranches)}`);
    let iosCcBranches = cciOSVersionInfos
      .filter(value => value.bmInfo !== undefined)
      .map(value => ({
        branch: `overseas/release/${value.version}`,
        versionCode: Number(version2VersionCode(value.version)),
        bmUserName: this.getBM(value.bmInfo, PlatformType.iOS),
      }));
    this.logger.info(`${this.TAG} ios cc: ${JSON.stringify(iosCcBranches)}`);

    // 往前再数一个版本
    const smallestLvVersionCode =
      iosLvBranches.length > 0
        ? iosLvBranches.reduce(
            (min, current) => (current.versionCode < min ? current.versionCode : min),
            iosLvBranches[0].versionCode,
          )
        : null;
    const smallestAdrLvVersionCode =
      iosLvBranches.length > 0
        ? androidLvBranches.reduce(
            (min, current) => (current.versionCode < min ? current.versionCode : min),
            androidLvBranches[0].versionCode,
          )
        : null;
    if (smallestLvVersionCode !== null) {
      const prevBranch = this.formatVersionCode(smallestLvVersionCode - 10);
      if (prevBranch !== null) {
        const prevBmInfo = await this.getBMFromBranch(`release/${prevBranch}`, PlatformType.iOS);
        if (prevBmInfo) {
          const lvPrevVersion = {
            branch: `release/${prevBranch}`,
            versionCode: smallestLvVersionCode - 10,
            bmUserName: this.getBM(prevBmInfo, PlatformType.iOS),
          };
          iosLvBranches.push(lvPrevVersion);
          const ccPrevVersion = {
            branch: `overseas/release/${versionUtils.lv2ccVersion(prevBranch)}`,
            versionCode: smallestLvVersionCode - 200 - 10,
            bmUserName: this.getBM(prevBmInfo, PlatformType.iOS),
          };
          iosCcBranches.push(ccPrevVersion);
        }
      }
    }
    if (smallestAdrLvVersionCode !== null) {
      const prevBranch = this.formatVersionCode(smallestAdrLvVersionCode - 10);
      if (prevBranch !== null) {
        const prevAndroidBmInfo = await this.getBMFromBranch(`release/${prevBranch}`, PlatformType.Android);
        if (prevAndroidBmInfo) {
          const lvPrevVersion = {
            branch: `release/${prevBranch}`,
            versionCode: smallestAdrLvVersionCode - 10,
            bmUserName: this.getBM(prevAndroidBmInfo, PlatformType.Android),
          };
          androidLvBranches.push(lvPrevVersion);
          const ccPrevVersion = {
            branch: `overseas/release/${versionUtils.lv2ccVersion(prevBranch)}`,
            versionCode: smallestAdrLvVersionCode - 200 - 10,
            bmUserName: this.getBM(prevAndroidBmInfo, PlatformType.Android),
          };
          androidCcBranches.push(ccPrevVersion);
        }
      }
    }

    this.logger.info(`${this.TAG} final android lv: ${JSON.stringify(androidLvBranches)}`);
    this.logger.info(`${this.TAG} final android cc: ${JSON.stringify(androidCcBranches)}`);
    this.logger.info(`${this.TAG} final ios lv: ${JSON.stringify(iosLvBranches)}`);
    this.logger.info(`${this.TAG} final ios cc: ${JSON.stringify(iosCcBranches)}`);
    // 搞点假数据。本地数据是错的
    // let androidLvBranches = [
    //   {
    //     branch: 'release/15.3.0',
    //     versionCode: 1450,
    //     bmUserName: 'zhengbolun.patlon',
    //   },
    //   {
    //     branch: 'release/14.4.0',
    //     versionCode: 1440,
    //     bmUserName: 'zhengbolun.patlon',
    //   },
    // ];
    // let androidCcBranches = [
    //   // {
    //   //   branch: 'overseas/release/11.3.0',
    //   //   versionCode: 1130,
    //   //   bmUserName: 'tanhaiyang',
    //   // },
    // ];
    // let iosLvBranches = [
    //   {
    //     branch: 'release/15.3.0',
    //     versionCode: 1450,
    //     bmUserName: 'zhengbolun.patlon',
    //   },
    //   {
    //     branch: 'release/15.2.0',
    //     versionCode: 144,
    //     bmUserName: 'zhengbolun.patlon',
    //   },
    // ];
    // let iosCcBranches = [
    //   {
    //     branch: 'overseas/release/13.2.0',
    //     versionCode: 1130,
    //     bmUserName: 'tanhaiyang',
    //   },
    // ];

    // 确保分支存在
    androidLvBranches = await this.checkBranchExist(androidLvBranches, repos.androidMainRepo.projectId);
    androidCcBranches = await this.checkBranchExist(androidCcBranches, repos.androidTTPMainRepo.projectId);
    iosLvBranches = await this.checkBranchExist(iosLvBranches, repos.iosMainRepo.projectId);
    iosCcBranches = await this.checkBranchExist(iosCcBranches, repos.iosTTPMainRepo.projectId);

    // 给出需要创建MR的分支数据: origin -> target
    const androidMRBranches = this.generateBranchFlowInfo(
      androidLvBranches,
      repos.androidMainRepo,
      androidCcBranches,
      repos.androidTTPMainRepo,
      PlatformType.Android,
    );
    const iosMRBranches = this.generateBranchFlowInfo(
      iosLvBranches,
      repos.iosMainRepo,
      iosCcBranches,
      repos.iosTTPMainRepo,
      PlatformType.iOS,
    );

    // const allMRBranches = [...androidMRBranches, ...iosMRBranches];
    // await this.createCheckFlowInfoTask(allMRBranches);
    this.logger.info(`${this.TAG} androidMRBranches: ${JSON.stringify(androidMRBranches)}`);
    this.logger.info(`${this.TAG} iosMRBranches: ${JSON.stringify(iosMRBranches)}`);
    const allNeedCreateMRBranches = await this.createCheckFlowInfoTaskForGitlab(androidMRBranches, iosMRBranches);
    // 给出真正需要创建MR的分支和类型
    // await this.redisClient.hdel(KEY_BRANCH_FLOW, 'iOS-release/15.5.0-rc/develop-zhengbolun.patlon');
    this.logger.info(`${this.TAG} allNeedCreateMRBranches: ${JSON.stringify(allNeedCreateMRBranches)}`);
    await this.createFlowTasks(allNeedCreateMRBranches);
  }

  private async createFlowTasks(allNeedCreateMRBranches: MultiMRBranchInfo[]) {
    // redis 中记录正在运行任务数据
    for (const branchInfo of allNeedCreateMRBranches) {
      const taskKey = this.createTaskKey(
        branchInfo.platform,
        branchInfo.origin,
        branchInfo.target,
        branchInfo.userName,
      );
      const exist = await this.redisClient.hexists(KEY_BRANCH_FLOW, taskKey);
      if (!exist) {
        if (
          await this.redisClient.hset(
            KEY_BRANCH_FLOW,
            taskKey,
            JSON.stringify([Math.floor(Date.now() / 1000).toString()]),
          )
        ) {
          this.logger.info(`${this.TAG} checkBranchFlow createMRTask ${taskKey}`);
          try {
            await this.createMRTask(
              branchInfo.platform,
              branchInfo.origin,
              branchInfo.target,
              branchInfo.userName,
              branchInfo.projectInfo,
            );
            teaCollect(TeaEvent.AUTO_FLOW, {
              platform: branchInfo.platform,
              type: 'create',
            });
          } catch (err) {
            // 可能create MR 失败，删除缓存Task标记，等下次重建
            this.logger.info(`${this.TAG} checkBranchFlow createMRFail ${taskKey} ${err}`);
            await this.redisClient.hdel(KEY_BRANCH_FLOW, taskKey);
          }
        }
      } else {
        this.logger.info(`${this.TAG} checkBranchFlow ${taskKey} is exist`);
      }
    }
  }

  // 定时触发, 暂定2分钟一次
  async checkFlowProgress() {
    // 非工作日不提醒
    const currentTime = Date.now();
    const isOffDay = await this.oncall.isOffDay(currentTime);
    if (isOffDay) {
      return;
    }
    const doCheckProgress = async (taskKey: string, progress: BranchFlowProgress) => {
      this.logger.info(`${this.TAG} checkFlowProgress: ${taskKey}, progress: ${progress.length}`);
      if (await this.checkMRClosed(taskKey, progress[1])) {
        return;
      }
      // 测试代码
      // this.checkMRConflictAndTimeout(taskKey, '6582112', '0');
      switch (progress.length) {
        case 1:
          // 'CheckFlowInfo'
          // 不用做任何事情，因为第一阶段是Task 结束后通知的
          break;
        case 2:
          // 'CreateMR'
          // 第二阶段主要是看有没有冲突
          if (await this.checkMRClosed(taskKey, progress[1])) {
            return;
          }
          this.checkMRConflict(taskKey, progress[1]);
          break;
        case 3:
          // 'ResolveConflicts'
          // 第三阶段不用做什么事情，等待MR超时后，就通知BM
          if (await this.checkMRClosed(taskKey, progress[1])) {
            return;
          }
          this.checkMRConflictAndTimeout(taskKey, progress[1], progress[2]);
          break;
        case 4:
          // 'pipeline'
          if (await this.checkMRClosed(taskKey, progress[1])) {
            return;
          }
          this.checkMRPipeline(taskKey, progress[1], progress[3]);
          break;
        case 5:
          // 'finished'
          if (await this.checkMRClosed(taskKey, progress[1])) {
            return;
          }
          break;
        default:
          break;
      }
    };

    const allTask = await this.redisClient.hkeys(KEY_BRANCH_FLOW);
    for (const taskKey of allTask) {
      const progressJson = await this.redisClient.hget(KEY_BRANCH_FLOW, taskKey);
      if (progressJson) {
        doCheckProgress(taskKey, JSON.parse(progressJson));
      }
    }
  }

  async clearTask() {
    const allTask = await this.redisClient.hkeys(KEY_BRANCH_FLOW);
    for (const taskKey of allTask) {
      await this.redisClient.hdel(KEY_BRANCH_FLOW, taskKey);
    }
  }

  async getPipeline(mrId: string): Promise<MrPipelineInfo[]> {
    const id = parseInt(mrId, 10);
    const mrRelationInfoList = await this.bitsService.getMrRelationList(id);
    let pipelinePromise: Promise<MrPipelineInfo[] | undefined>[] = [];
    if (mrRelationInfoList) {
      pipelinePromise = mrRelationInfoList.map(value => this.bitsService.getMrPipelineList(value.id.toString()));
    }
    pipelinePromise.push(this.bitsService.getMrPipelineList(mrId));
    const pipelines: (MrPipelineInfo[] | undefined)[] = await Promise.all(pipelinePromise);
    let result: MrPipelineInfo[] = [];
    pipelines.forEach(value => {
      if (value) {
        result = result.concat(value);
      }
    });
    return result;
  }

  async getTaskInfo(): Promise<FlowInfo[]> {
    const allTask = await this.redisClient.hkeys(KEY_BRANCH_FLOW);
    const result: FlowInfo[] = [];
    for (const taskKey of allTask) {
      const progressJson = await this.redisClient.hget(KEY_BRANCH_FLOW, taskKey);
      if (progressJson) {
        const branchInfo = this.getBranchInfoByTask(taskKey);
        const progressInfo: string[] = JSON.parse(progressJson);
        if (progressInfo.length > 1) {
          const mrId = progressInfo[1];
          const id = parseInt(mrId, 10);
          const timestamp = parseInt(progressInfo[0]);
          const mrInfo = await this.bitsService.getMrInfo({
            mrId: id,
          });
          const pipelineInfo = await this.getPipeline(mrId);
          const userInfo = await this.larkService.getUserInfoByEmail(branchInfo.userName.concat('@bytedance.com'));
          if (mrInfo && pipelineInfo && userInfo) {
            result.push({
              pipeline: pipelineInfo,
              mrUrl: mrInfo.mr_detail_url,
              mrTitle: mrInfo.title,
              source: branchInfo.origin,
              target: branchInfo.target,
              progress: progressInfo.length + 1,
              timestamp,
              userInfo,
            });
          }
        }
      }
    }
    return result;
  }

  private async createCheckFlowInfoTaskForGitlab(androidMRBranches: MRBranchInfo[], iOSMRBranches: MRBranchInfo[]) {
    const flowInfos: MultiMRBranchInfo[] = [];
    // 先遍历Android
    let diffResultsPromise = androidMRBranches.map(branchInfo =>
      this.gitlabService.checkBranchDiffList(
        PlatformType.Android,
        branchInfo.target,
        branchInfo.origin,
        branchInfo.projectInfo,
      ),
    );
    this.logger.info(`${this.TAG} createCheckFlowInfoTaskForGitlab android`);
    let diffResults = await Promise.all(diffResultsPromise);
    for (let i = 0; i < androidMRBranches.length; i++) {
      const branchInfo = androidMRBranches[i];
      const repoList = diffResults[i];
      if (repoList.length > 0) {
        flowInfos.push({
          projectInfo: branchInfo.projectInfo,
          origin: branchInfo.origin,
          target: branchInfo.target,
          platform: PlatformType.Android,
          userName: branchInfo.userName,
        });
      }
    }
    // 遍历iOS 时判断是否已经有Android了
    diffResultsPromise = iOSMRBranches.map(branchInfo =>
      this.gitlabService.checkBranchDiffList(
        PlatformType.iOS,
        branchInfo.target,
        branchInfo.origin,
        branchInfo.projectInfo,
      ),
    );
    this.logger.info(`${this.TAG} createCheckFlowInfoTaskForGitlab ios`);
    diffResults = await Promise.all(diffResultsPromise);
    for (let i = 0; i < iOSMRBranches.length; i++) {
      const branchInfo = iOSMRBranches[i];
      const repoList = diffResults[i];
      if (repoList.length > 0) {
        const androidInfo = flowInfos.find(
          info => info.origin === branchInfo.origin && info.target === branchInfo.target,
        );
        if (
          androidInfo &&
          (await this.checkCommonRepoDiff(
            androidInfo.target,
            androidInfo.origin,
            androidInfo.projectInfo === repos.androidTTPMainRepo,
          ))
        ) {
          androidInfo.platform = PlatformType.Multi;
        } else {
          flowInfos.push({
            projectInfo: branchInfo.projectInfo,
            origin: branchInfo.origin,
            target: branchInfo.target,
            platform: PlatformType.iOS,
            userName: branchInfo.userName,
          });
        }
      }
    }
    return flowInfos;
  }

  private async creatAutoResolveConflictsTask(
    mrId: number,
    projectId: number,
    sourceBranch: string,
    targetBranch: string,
    hasRtSDK: boolean,
    hasRetouchMiddleware: boolean,
  ) {
    if (projectId === repos.iosMainRepo.projectId || projectId === repos.iosTTPMainRepo.projectId) {
      this.codeProcessor.triggerJob(
        createLVIOSResolveConflicts(mrId, projectId, sourceBranch, targetBranch, hasRtSDK, hasRetouchMiddleware),
      );
    } else if (projectId === repos.pcMainRepo.projectId || projectId === repos.pcTTPMainRepo.projectId) {
      this.codeProcessor.triggerJob(createLVPCResolveConflicts(mrId, projectId, sourceBranch, targetBranch));
    }
    const projectRepo = repos.searchProjectInfo({ projectId });
    if (!projectRepo) {
      throw new Error(`creatAutoResolveConflictsTask: projectId: ${projectId} not found`);
    }
    return this.codeProcessor.triggerJob(
      createLVAndroidResolveConflictsV2(mrId, projectId, projectRepo.projectName, sourceBranch, targetBranch),
    );
  }

  async createMRTask(
    platform: PlatformType,
    origin: string,
    target: string,
    userName: string,
    projectInfo: ProjectInfo,
  ) {
    let res: NetworkResult<CreateMrResult> | undefined;
    if (platform === PlatformType.Multi) {
      res = await this.createFlowMR(
        origin,
        target,
        userName,
        [PlatformType.Android, PlatformType.iOS],
        false,
        projectInfo,
      );
    } else if (platform === PlatformType.Android) {
      res = await this.createFlowMR(origin, target, userName, [PlatformType.Android], false, projectInfo);
    } else if (platform === PlatformType.iOS) {
      res = await this.createFlowMR(origin, target, userName, [PlatformType.iOS], false, projectInfo);
    }
    if (res && res.code === 0 && res.data) {
      await this.updateRedisCacheInfo(
        this.createTaskKey(platform, origin, target, userName),
        res.data.mr_id.toString(),
        2,
      );
    } else {
      throw new Error(`Failed to create mr: ${res?.message ?? ''}`);
    }
  }

  private async checkMRConflict(taskKey: string, mrId: string) {
    const id = parseInt(mrId, 10);
    const { platform } = this.getBranchInfoByTask(taskKey);
    const mrInfo = await this.bitsService.getMrInfo({
      mrId: id,
    });
    const mrRelationInfoList = await this.bitsService.getMrRelationList(id);
    this.logger.info(`${this.TAG} mrRelationInfoList ${JSON.stringify(mrRelationInfoList)}`);
    if (mrInfo) {
      if (mrInfo.conflicted === ConflictedType.check_not_complete) {
        // 检查冲突中，继续等待
        this.logger.info(`${this.TAG} checkMRConflict: check_not_complete`);
        return;
      } else if (mrInfo.conflicted === ConflictedType.conflicted) {
        // 有冲突，进入第三阶段，启动解决的Job 并设置Job ID
        this.logger.info(`${this.TAG} checkMRConflict: conflicted`);
        // iOS解冲突的job需要添加参数
        let hasRtSDK = false;
        let hasRetouchMiddleware = false;
        if (mrRelationInfoList) {
          for (let i = 0; i < mrRelationInfoList.length; i++) {
            if (mrRelationInfoList[i].project_id === 513962) {
              hasRtSDK = true;
            } else if (mrRelationInfoList[i].project_id === 134284) {
              hasRetouchMiddleware = true;
            }
          }
        }
        if (mrRelationInfoList) {
          for (let i = 0; i < mrRelationInfoList.length; i++) {
            const relationInfo = mrRelationInfoList[i];
            const jobId = await this.creatAutoResolveConflictsTask(
              mrInfo.id,
              relationInfo.project_id,
              relationInfo.from_branch,
              relationInfo.target_branch,
              hasRtSDK,
              hasRetouchMiddleware,
            );
            if (jobId) {
              this.logger.info(`${this.TAG} creatAutoResolveConflictsTask: ${jobId}`);
            } else {
              // 没有job的情况下，merge一次target，防止子仓不同步的情况出现
              const result = await this.bitsService.mergeTarget(relationInfo.project_id, relationInfo.iid);
              this.logger.info(`${this.TAG} mrId: ${relationInfo.id}, merge target result: ${result}`);
            }
          }
        }
        const jobId = await this.creatAutoResolveConflictsTask(
          mrInfo.id,
          mrInfo.project_id,
          mrInfo.from_branch,
          mrInfo.target_branch,
          hasRtSDK,
          hasRetouchMiddleware,
        );
        this.logger.info(`${this.TAG} creatAutoResolveConflictsTask: ${jobId}`);
        await this.updateRedisCacheInfo(taskKey, '0', 3);
        teaCollect(TeaEvent.AUTO_FLOW, {
          platform,
          type: 'conflict',
        });
      } else if (mrInfo.conflicted === ConflictedType.not_conflicted) {
        // 没有冲突，将MR_ID设置到第四阶段，等待Pipeline结束
        this.logger.info(`${this.TAG} checkMRConflict: not conflicted`);
        await this.updateRedisCacheInfo(taskKey, '0', 4);
        teaCollect(TeaEvent.AUTO_FLOW, {
          platform,
          type: 'not_conflict',
        });
      }
    }
  }

  private getVersionFromTaskKey(taskKey: string) {
    const taskKeyParts = taskKey.split('-');
    if (taskKeyParts.length < 4) {
      this.logger.error(`${this.TAG} getVersionByTaskKey: ${taskKey}, taskKeyParts.length < 4`);
      return;
    }
    const origin = taskKeyParts[1];
    const originParts = origin.split('/');
    if (originParts.length < 2) {
      this.logger.error(`${this.TAG} getVersionByTaskKey: ${taskKey}, originParts.length < 2, origin: ${origin}`);
      return;
    }
    return originParts[originParts.length - 1];
  }

  private async checkMRConflictAndTimeout(taskKey: string, mrId: string, notifyType: string) {
    const mrInfo = await this.bitsService.getMrInfo({
      mrId: parseInt(mrId, 10),
    });

    const id = parseInt(mrId, 10);
    let relationMrConflicted = false;
    const mrRelationInfoList = await this.bitsService.getMrRelationList(id);
    if (mrRelationInfoList && mrRelationInfoList.length > 0) {
      const checkConflictsPromises = mrRelationInfoList.map(async relationInfo => {
        const relationMrInfo = await this.bitsService.getMrInfo({
          mrId: relationInfo.id,
        });
        if (relationMrInfo && relationMrInfo.conflicted === ConflictedType.conflicted) {
          relationMrConflicted = true;
        }
      });
      await Promise.all(checkConflictsPromises);
    }

    if (!relationMrConflicted && mrInfo && mrInfo.conflicted === ConflictedType.not_conflicted) {
      await this.updateRedisCacheInfo(taskKey, '0', 4);
      return;
    }

    if (mrInfo && notifyType === '0') {
      if (Math.floor(Date.now() / 1000) - mrInfo.create_time > 10 * 60) {
        const autoFlowGroupChat = await this.getAutoFlowGroupChatId(taskKey);
        if (!autoFlowGroupChat) {
          this.logger.error('autoFlowGroupChat error, autoFlowGroupChat is undefined');
          return;
        }
        this.larkCardService
          .buildMrConflictInformCard(mrInfo.id, mrInfo.from_branch, mrInfo.target_branch, [mrInfo.platform], mrInfo)
          .then(async (card: Card) => {
            this.larkService.sendMessage(
              UserIdType.chatId,
              autoFlowGroupChat,
              JSON.stringify(card),
              MessageType.interactive,
            );
          });
        if (mrRelationInfoList) {
          for (const relationInfo of mrRelationInfoList) {
            await this.sendConflictCardToAuthor(relationInfo.id, taskKey);
          }
        }

        await this.sendConflictCardToAuthor(id, taskKey);
        await this.updateRedisCacheInfo(taskKey, '1', 3);
      } else if (mrInfo.conflicted !== ConflictedType.check_not_complete) {
        // 重新触发一次冲突检测
        await this.sendConflictDetectRequest(mrInfo.project_id.toString(), mrInfo.iid.toString(), 'zhengbolun.patlon');
      }
    }
  }

  async printMRInfo(mrId: string) {
    const id = parseInt(mrId, 10);
    const mrInfo = await this.bitsService.getMrInfo({
      mrId: id,
    });
    const mrRelationInfoList = await this.bitsService.getMrRelationList(id);
    this.logger.info(`${this.TAG} mrRelationInfoList ${JSON.stringify(mrRelationInfoList)}`);
    return mrInfo;
  }

  private async checkMRPipeline(taskKey: string, mrId: string, notifyType: string) {
    const id = parseInt(mrId, 10);
    const mrInfo = await this.bitsService.getMrInfo({
      mrId: id,
    });
    const { platform } = this.getBranchInfoByTask(taskKey);
    if (mrInfo) {
      // mrInfo.pipeline_status: running, failed, success
      if (mrInfo.pipeline_status === 'failed' && notifyType === '0') {
        // this.larkCardService
        //   .buildMrPipelineFailedInformCard(
        //     mrInfo.id,
        //     mrInfo.from_branch,
        //     mrInfo.target_branch,
        //     [mrInfo.platform],
        //     mrInfo,
        //   )
        //   .then(async (card: Card) => {
        //     this.larkService.sendMessage(UserIdType.chatId, BM_CHAT_ID, JSON.stringify(card), MessageType.interactive);
        //   });
        teaCollect(TeaEvent.AUTO_FLOW, {
          platform,
          type: 'pipeline_failed',
        });
        this.updateRedisCacheInfo(KEY_BRANCH_FLOW, '1', 4);
      } else if (mrInfo.pipeline_status === 'success') {
        await this.updateRedisCacheInfo(taskKey, mrId, 5);
        teaCollect(TeaEvent.AUTO_FLOW, {
          platform,
          type: 'pipeline_success',
        });
      }
    }
  }

  private async checkMRClosed(taskKey: string, mrId: string): Promise<boolean> {
    const id = parseInt(mrId, 10);
    const mrInfo = await this.bitsService.getMrInfo({
      mrId: id,
    });
    const { platform } = this.getBranchInfoByTask(taskKey);

    let timeoutCount = 1;
    let canSendCard = false;
    const exist = await this.redisClient.hexists(`${KEY_BRANCH_FLOW}_timeout`, `${taskKey}_timeout`);
    if (!exist) {
      if (await this.redisClient.hset(`${KEY_BRANCH_FLOW}_timeout`, `${taskKey}_timeout`, 1)) {
        this.logger.info(`${this.TAG} checkMrClosed taskKey timeout ${taskKey}_timeout`);
        canSendCard = true;
      }
    } else {
      this.logger.info(`${this.TAG} checkMrClosed ${taskKey}_timeout is exist`);
      const countFromRedis = await this.redisClient.hget(`${KEY_BRANCH_FLOW}_timeout`, `${taskKey}_timeout`);
      timeoutCount = countFromRedis !== null ? Number(countFromRedis) : 1;
      if (countFromRedis === null) {
        canSendCard = false;
      } else {
        canSendCard = true;
      }
    }

    if (mrInfo) {
      if (mrInfo.state === MrState.closed) {
        this.finishMR(false, mrInfo, taskKey);
        teaCollect(TeaEvent.AUTO_FLOW, {
          platform,
          type: 'close',
        });
        return true;
      } else if (mrInfo.state === MrState.merged) {
        this.finishMR(true, mrInfo, taskKey);
        teaCollect(TeaEvent.AUTO_FLOW, {
          platform,
          type: 'finish',
        });
        return true;
      } else if (Math.floor(Date.now() / 1000) - mrInfo.create_time > timeoutCount * 180 * 60 && canSendCard) {
        await this.redisClient.hset(`${KEY_BRANCH_FLOW}_timeout`, `${taskKey}_timeout`, timeoutCount + 1);
        // 非工作日不提醒
        const currentTime = Date.now();
        const isOffDay = await this.oncall.isOffDay(currentTime);
        if (isOffDay) {
          return false;
        }
        // 三小时还没合入则通知BM
        const chatGroup = await this.getAutoFlowGroupChatId(taskKey);
        let sendGroup = BM_CHAT_ID;
        if (chatGroup) {
          sendGroup = chatGroup;
        }
        const originBranch = this.getBranchInfoByTask(taskKey);
        const bmInfo = await this.getBMFromBranch(originBranch.origin, mrInfo.platform);
        if (!bmInfo) {
          this.logger.error('getBM error, bmInfo is undefined');
          return false;
        }
        const rdBMList = new Set<string>();
        const platformList = new Set<PlatformType>();
        rdBMList.add(this.getBM(bmInfo, mrInfo.platform));
        platformList.add(mrInfo.platform);
        const mrRelationInfoList = await this.bitsService.getMrRelationList(mrInfo.id);
        if (mrRelationInfoList && mrRelationInfoList.length > 0) {
          for (const relationInfo of mrRelationInfoList) {
            if (
              repos.androidMainRepo.projectId === relationInfo.project_id ||
              repos.androidTTPMainRepo.projectId === relationInfo.project_id
            ) {
              const androidBMInfo = await this.getBMFromBranch(originBranch.origin, PlatformType.Android);
              if (androidBMInfo) {
                rdBMList.add(this.getBM(androidBMInfo, PlatformType.Android));
                platformList.add(PlatformType.Android);
              }
            } else if (
              repos.iosMainRepo.projectId === relationInfo.project_id ||
              repos.iosTTPMainRepo.projectId === relationInfo.project_id
            ) {
              const iOSBMInfo = await this.getBMFromBranch(originBranch.origin, PlatformType.iOS);
              if (iOSBMInfo) {
                rdBMList.add(this.getBM(iOSBMInfo, PlatformType.iOS));
                platformList.add(PlatformType.iOS);
              }
            } else if (
              repos.pcMainRepo.projectId === relationInfo.project_id ||
              repos.pcTTPMainRepo.projectId === relationInfo.project_id
            ) {
              const pcBMInfo = await this.getBMFromBranch(originBranch.origin, PlatformType.PC);
              if (pcBMInfo) {
                rdBMList.add(this.getBM(pcBMInfo, PlatformType.PC));
                platformList.add(PlatformType.PC);
              }
            }
          }
        }

        this.larkCardService
          .buildMrTimeoutInformCard(
            mrInfo.id,
            mrInfo.from_branch,
            mrInfo.target_branch,
            Array.from(platformList),
            mrInfo,
            Array.from(rdBMList),
          )
          .then(async (card: Card) => {
            this.larkService.sendMessage(UserIdType.chatId, sendGroup, JSON.stringify(card), MessageType.interactive);
          });
        return false;
      }
    }
    return false;
  }

  async finishMR(success: boolean, mrInfo: MrInfo, taskKey: string) {
    // if (!success) {
    //   // 未成功合入
    // } else {
    //   // 成功合入
    // }
    this.larkCardService
      .buildMrFinishInformCard(success, mrInfo.from_branch, mrInfo.target_branch, [mrInfo.platform], mrInfo)
      .then(async (card: Card) => {
        this.larkService.sendMessage(UserIdType.chatId, BM_CHAT_ID, JSON.stringify(card), MessageType.interactive);
      });
    await this.redisClient.hdel(KEY_BRANCH_FLOW, taskKey);
    await this.redisClient.hdel(`${KEY_BRANCH_FLOW}_timeout`, `${taskKey}_timeout`);
  }

  private createTaskKey(platform: PlatformType, originBranch: string, targetBranch: string, username: string): string {
    return `${platform}-${originBranch}-${targetBranch}-${username}`;
  }

  private createProgressCheckFlowInfo(jobId: number): BranchFlowProgress {
    return [jobId.toString()];
  }

  private async updateRedisCacheInfo(taskKey: string, progressInfo: string, progressIndex: number) {
    const jsonProgress = await this.redisClient.hget(KEY_BRANCH_FLOW, taskKey);
    const index = progressIndex - 1;
    if (jsonProgress) {
      const progresss: BranchFlowProgress = JSON.parse(jsonProgress);
      if (index < progresss.length) {
        progresss[index] = progressInfo;
      } else {
        progresss.push(progressInfo);
      }
      await this.redisClient.hset(KEY_BRANCH_FLOW, taskKey, JSON.stringify(progresss));
    }
  }

  lv2retouchVersion(lvVersion: string): string {
    const num = Number(lvVersion.substring(0, lvVersion.length - 4)) - 4;
    return num.toString().concat(lvVersion.substring(lvVersion.length - 4, lvVersion.length));
  }

  cc2retouchVersion(ccVersion: string): string {
    const num = Number(ccVersion.substring(0, ccVersion.length - 4)) - 2;
    return num.toString().concat(ccVersion.substring(ccVersion.length - 4, ccVersion.length));
  }

  getIPhotoTargetBranch(targetBranch: string) {
    if (isDevelopBranch(targetBranch)) {
      return 'develop';
    } else if (!targetBranch.includes('oversea')) {
      const version = targetBranch.split('/');
      if (version.length > 1) {
        const lvVersion = version[1];
        const iPhotoVersion = this.lv2retouchVersion(lvVersion);
        return `Release/${iPhotoVersion}`;
      }
    } else {
      const version = targetBranch.split('/');
      if (version.length > 2) {
        const ccVersion = version[2];
        const iPhotoVersion = this.cc2retouchVersion(ccVersion);
        return `Release/${iPhotoVersion}`;
      }
    }
    return '';
  }

  getIPhotoSubRepoBranch(targetBranch: string) {
    if (isDevelopBranch(targetBranch)) {
      return 'develop';
    } else if (!targetBranch.includes('oversea')) {
      const version = targetBranch.split('/');
      if (version.length > 1) {
        const lvVersion = version[1];
        const iPhotoVersion = this.lv2retouchVersion(lvVersion);
        return `release/retouch/${iPhotoVersion}`;
      }
    } else {
      const version = targetBranch.split('/');
      if (version.length > 2) {
        const ccVersion = version[2];
        const iPhotoVersion = this.cc2retouchVersion(ccVersion);
        return `release/retouch/${iPhotoVersion}`;
      }
    }
    return '';
  }

  getRetouchTargetBranch(targetBranch: string) {
    if (isDevelopBranch(targetBranch)) {
      return 'develop';
    } else if (!targetBranch.includes('oversea')) {
      const version = targetBranch.split('/');
      if (version.length > 1) {
        const lvVersion = version[1];
        const iPhotoVersion = this.lv2retouchVersion(lvVersion);
        return `release/retouch/${iPhotoVersion}`;
      }
    } else {
      const version = targetBranch.split('/');
      if (version.length > 2) {
        const ccVersion = version[2];
        const iPhotoVersion = this.cc2retouchVersion(ccVersion);
        return `release/retouch/${iPhotoVersion}`;
      }
    }
    return '';
  }

  async createFlowMR(
    sourceBranch: string,
    targetBranch: string,
    userName: string,
    platforms: PlatformType[],
    raiseReview: boolean,
    projectInfo: ProjectInfo,
  ): Promise<NetworkResult<CreateMrResult>> {
    const target_branch = targetBranch.split('/');
    const source_branch = sourceBranch.split('/');

    let pc_source_branch = sourceBranch;
    let pc_target_branch = targetBranch;

    const isOversea = repos.oversea_main_repos.includes(projectInfo);

    // 获取所有平台的repo
    const allRepos: ProjectInfo[] = _(
      zip(
        [PlatformType.Android, PlatformType.iOS, PlatformType.PC],
        [
          isOversea ? repos.androidOverseasRepos : repos.androidRepos,
          isOversea ? repos.iosOverseaRepos : repos.iosRepos,
          isOversea ? repos.pcOverseasRepos : repos.pcRepos,
        ],
      ),
    )
      .filter(([p]) => Boolean(p && platforms.includes(p)))
      .map(([, r]) => r)
      .compact()
      .flatten()
      .uniq()
      .value();

    // 拿到分支有差异的repo
    let hasDiffRepo: ProjectInfo[] = [];
    for (const repo of allRepos) {
      if (repo.projectName === 'faceu-ios/LVUniteAd') {
        if (platforms.length > 1 || !platforms.includes(PlatformType.iOS) || sourceBranch.includes('oversea')) {
          continue;
        }
      }
      if (repo === repos.androidMainRepo || repo === repos.androidTTPMainRepo) {
        if (await this.checkAndroidDiff(repo, targetBranch, sourceBranch)) {
          hasDiffRepo.push(repo);
        }
      } else {
        let ret;
        if (isDevelopBranch(targetBranch)) {
          const compareBranch = isOversea ? repo.ttpDefaultBranch : repo.defaultBranch;
          ret = await this.gitlabService.compare(`${repo.projectId}`, compareBranch, sourceBranch, false);
        } else {
          ret = await this.gitlabService.compare(`${repo.projectId}`, targetBranch, sourceBranch, false);
        }
        if (ret.code === NetworkCode.Error) {
          throw new Error(ret.message);
        }
        if (ret.data?.diffs && ret.data?.diffs.length > 0) {
          // 如果目标分支是cc主仓，则不回流醒图的sdk
          const isRetouchSdk = repo.projectName.includes('retouch_sdk');
          if (isOversea && isRetouchSdk) {
            continue;
          }
          hasDiffRepo.push(repo);
        }
      }
    }

    if (hasDiffRepo.length === 0) {
      throw new Error('no diffs found');
    }

    // 不是海外分支的情况下，有一个公共子仓，就带上PC
    if (hasDiffRepo.some(v => repos.commonSubRepos.includes(v))) {
      if (platforms.indexOf(PlatformType.PC) === -1) {
        platforms.push(PlatformType.PC);
      }
      if (platforms.indexOf(PlatformType.Android) === -1) {
        platforms.push(PlatformType.Android);
      }
      if (platforms.indexOf(PlatformType.iOS) === -1) {
        platforms.push(PlatformType.iOS);
      }
    }

    let hasIphoto = false;
    let hasUniteAd = false;
    // let iphotoTargetBranch = '';
    if (platforms.length === 1 && platforms[0] === PlatformType.iOS) {
      if (hasDiffRepo.some(v => v.projectName.includes('RetouchMiddleware') || v.projectName.includes('rtsdk'))) {
        hasIphoto = false;
      } else {
        // 只有iOS的情况下, 且原来没有RetouchMiddleware和rtsdk的回流，如果存在LVUnitedAd的差异，发起跟醒图一起的多主仓mr
        if (hasDiffRepo.some(v => v.projectName === 'faceu-ios/LVUniteAd')) {
          hasUniteAd = true;
          // hasIphoto = true;
          // iphotoTargetBranch = this.getIPhotoTargetBranch(targetBranch);
          // hasDiffRepo = hasDiffRepo.concat(repos.iosRetouchRepos);
          // for (const repo of repos.iPhotoSubRepos) {
          //   hasDiffRepo.push(repo);
          // }
        }
      }
    }

    // 多宿主MR保证至少有一个公共子仓, 否则多仓MR拉不出来
    // 充数的子仓不用加上PC
    if (platforms.length > 1) {
      if (!hasDiffRepo.some(v => repos.commonSubRepos.includes(v))) {
        hasDiffRepo = hasDiffRepo.concat(repos.commonSubRepos[0]);
      }
    }

    let canAddPCForMiddleLayerPC = true;
    if (
      platforms.length > 1 &&
      platforms.indexOf(PlatformType.PC) !== -1 &&
      sourceBranch.includes('release') &&
      (targetBranch.includes('release') || isDevelopBranch(targetBranch))
    ) {
      // 获取pc的source和target对应的版本号
      const pc_source_version = lv2PCVersion(source_branch[source_branch.length - 1]);
      pc_source_branch = isOversea ? `oversea/release/${pc_source_version}` : `release/${pc_source_version}`;
      // pc_source_branch = 'release/7.3.0';
      if (!isDevelopBranch(targetBranch)) {
        const pc_target_version = lv2PCVersion(target_branch[target_branch.length - 1]);
        pc_target_branch = isOversea ? `oversea/release/${pc_source_version}` : `release/${pc_target_version}`;
        canAddPCForMiddleLayerPC =
          (await this.gitlabService.getBranchInfo(repos.middleLayerRepo.projectId, `pc/${pc_target_version}`)).code !==
          0;
      } else {
        pc_target_branch = 'rc/develop';
      }
    }

    // 主仓、公共子仓的分支需要创建，不然提 mr 麻烦
    for (const plat of platforms) {
      if (plat === PlatformType.Android) {
        isOversea ? hasDiffRepo.push(repos.androidTTPMainRepo) : hasDiffRepo.push(repos.androidMainRepo);
      } else if (plat === PlatformType.iOS) {
        isOversea ? hasDiffRepo.push(repos.iosTTPMainRepo) : hasDiffRepo.push(repos.iosMainRepo);
      } else {
        isOversea ? hasDiffRepo.push(repos.pcTTPMainRepo) : hasDiffRepo.push(repos.pcMainRepo);
      }
    }

    hasDiffRepo = Array.from(new Set(hasDiffRepo));

    // 如果中间层已经切出 pc/xxxx 分支，分支回流先不带PC，后续PC分支进行单独回流
    if (!canAddPCForMiddleLayerPC) {
      hasDiffRepo = hasDiffRepo.filter(repo => repo.projectId !== repos.pcMainRepo.projectId);
      hasDiffRepo = hasDiffRepo.filter(repo => repo.projectId !== repos.pcTTPMainRepo.projectId);
    }

    // 创建中间分支
    const mappingName = (name: string) => name.split('/').join('_');

    const midBranch = `p/robot.merge/${mappingName(sourceBranch)}_to_${mappingName(targetBranch)}_${nanoid(6)}`;

    for (const item of hasDiffRepo) {
      if (item.projectName === 'faceu-ios/iPhoto') {
        const iphotoSourceBranch = this.getIPhotoTargetBranch(sourceBranch);
        const ret = await this.gitlabService.createBranch(item.projectId, midBranch, iphotoSourceBranch);
        if (ret.code === NetworkCode.Error) {
          throw new Error(ret.message);
        }
        continue;
      } else if (hasIphoto && (item.projectName.includes('RetouchMiddleware') || item.projectName.includes('rtsdk'))) {
        const iphotoSourceBranch = this.getIPhotoSubRepoBranch(sourceBranch);
        const ret = await this.gitlabService.createBranch(item.projectId, midBranch, iphotoSourceBranch);
        if (ret.code === NetworkCode.Error) {
          throw new Error(ret.message);
        }
        continue;
      }
      const ret = await this.gitlabService.createBranch(
        item.projectId,
        midBranch,
        item.platform === PlatformType.PC ? pc_source_branch : sourceBranch,
      );
      if (ret.code === NetworkCode.Error) {
        throw new Error(ret.message);
      }
    }

    this.logger.info(
      `raiseMr ${sourceBranch} -> ${targetBranch}] [author: ${userName}] [diffRepos]: ${JSON.stringify(hasDiffRepo)}`,
    );

    // 需要提mr的repo以MrRepos形式存起来
    const mrRepos: MrRepos[] = [];

    // if (hasIphoto) {
    //   hasDiffRepo = hasDiffRepo.filter(
    //     repo =>
    //       repo.projectName === 'faceu-ios/LVUniteAd' ||
    //       repo.projectName === 'faceu-ios/iPhoto' ||
    //       repo.projectName === 'faceu-ios/iMovie',
    //   );
    // }

    for (const repo of hasDiffRepo) {
      const isToDevelop = isDevelopBranch(targetBranch);
      let newTargetBranch: string;
      if (isToDevelop) {
        newTargetBranch = isOversea ? repo.ttpDefaultBranch : repo.defaultBranch;
      } else {
        newTargetBranch = targetBranch;
      }
      // PC 逻辑
      if (repo.platform === PlatformType.PC) {
        newTargetBranch = pc_target_branch;
      }
      // 醒图逻辑
      if (repo.projectName === 'faceu-ios/iPhoto') {
        mrRepos.push({
          projectId: repo.projectId,
          sourcesBranch: midBranch,
          targetBranch: '',
          isHost: true,
          platform: repo.platform,
        });
      } else if (hasIphoto && (repo.projectName.includes('RetouchMiddleware') || repo.projectName.includes('rtsdk'))) {
        const iPhotoSubRepoBranch = this.getIPhotoSubRepoBranch(targetBranch);
        mrRepos.push({
          projectId: repo.projectId,
          sourcesBranch: midBranch,
          targetBranch: iPhotoSubRepoBranch,
          isHost: false,
          platform: repo.platform,
        });
      } else {
        // 默认逻辑
        mrRepos.push({
          projectId: repo.projectId,
          sourcesBranch: midBranch,
          targetBranch: newTargetBranch,
          isHost: repos.main_repos.includes(repo),
          platform: repo.platform,
        });
      }
    }

    let target_version = source_branch[source_branch.length - 1];
    if (!isDevelopBranch(targetBranch)) {
      target_version = target_branch[target_branch.length - 1];
    }

    const mrParams = await this.bitsConfigV2Service.buildMrConfig({
      title: `AutoFlow: ${sourceBranch} -> ${targetBranch} [中间分支： ${midBranch}]`,
      repos: mrRepos,
      targetVersion: target_version,
      author: userName,
      type: MrType.merge,
      customFields: {
        range_of_influence: '纸飞机后台发起MR，和对应操作人进行确认',
        how_to_test_issue: '纸飞机后台发起MR，和对应操作人进行确认',
        the_test_results: 1,
        disable_compress_ld_for_inhouse: true, // iOS 屏蔽压缩链接器
        MR_FORBID_PACKAGE_CHECK: true, // iOS 屏蔽包大小检测
      },
      wip: false,
      review_start_type: raiseReview ? 0 : 1,
    });

    if (hasIphoto) {
      mrParams.review_fetch_mode = 2;
      for (const host of mrParams.hosts) {
        if (
          host.project_gitlab_id === repos.iosMainRepo.projectId ||
          host.project_gitlab_id === repos.iosTTPMainRepo.projectId
        ) {
          // mr_dependencies中去除project_gitlab_id 为134284和513962的依赖
          host.mr_dependencies = host.mr_dependencies.filter(
            item => item.project_gitlab_id !== 134284 && item.project_gitlab_id !== 513962,
          );
        }
      }
      // mrParams.type = MrType.optimize;
      // mrParams.mr_reviewers = this.setLVUniteAdReviewer(userName);
      mrParams.mr_reviewers = [];
    } else {
      mrParams.mr_reviewers = [];
      mrParams.review_fetch_mode = 2;
    }

    this.logger.info(
      `raiseMr ${sourceBranch} -> ${targetBranch}] [author: ${userName}] [diffParams]: ${JSON.stringify(mrParams)}`,
    );

    const res = await this.bitsService.createMr(mrParams, userName);

    if (res.code === 0 && res.data) {
      const mrInfo: MrInfo | undefined = await this.bitsService.getMrInfo({
        mrId: res.data.mr_id,
      });
      if (hasUniteAd) {
        const card = await this.larkCardService.buildLVUniteAdMrCreateInformCard(
          res.data.mr_id,
          sourceBranch,
          targetBranch,
          platforms,
          mrInfo,
        );
        const userId = await this.larkService.getUserIdByEmail('<EMAIL>');
        if (!userId) {
          this.logger.error(`[Version Release] [sendPociOSIntegrationTestStage] poc user id not found`);
          return res;
        }
        if (userId.user_id) {
          const sendRes = await this.larkService.sendCardMessage(UserIdType.userId, userId.user_id, card);
          this.logger.info(
            `raiseMr ${sourceBranch} -> ${targetBranch}] [author: ${userName}] [sendRes]: ${JSON.stringify(sendRes)}`,
          );
        }
        this.logger.info(
          `raiseMr ${sourceBranch} -> ${targetBranch}] [author: ${userName}] [res]: ${JSON.stringify(res)}`,
        );
        // 需要给醒图安卓也发起一次回流
        // await this.retouchAutoFlow(targetBranch, sourceBranch);
      }
      this.larkCardService
        .buildMrCreateInformCard(res.data.mr_id, sourceBranch, targetBranch, platforms, mrInfo)
        .then(async (card: Card) => {
          await this.larkService.sendMessage(
            UserIdType.chatId,
            BM_CHAT_ID,
            JSON.stringify(card),
            MessageType.interactive,
          );
        });
    } else {
      const card = await this.larkCardService.buildBaseCard({
        title: `MR创建失败, sourceBranch: ${sourceBranch}, targetBranch: ${targetBranch}`,
        template: CardTemplate.red,
      });
      const userId = await this.larkService.getUserIdByEmail('<EMAIL>');
      if (!userId) {
        this.logger.error(`[Version Release] [sendPociOSIntegrationTestStage] poc user id not found`);
        return res;
      }
      if (userId.user_id) {
        const sendRes = await this.larkService.sendCardMessage(UserIdType.userId, userId.user_id, card);
      }
      this.logger.error(
        `raiseMr ${sourceBranch} -> ${targetBranch}] [author: ${userName}] [res]: ${JSON.stringify(res)}, params: ${JSON.stringify(
          mrParams,
        )}`,
      );
    }

    this.logger.info(`raiseMr ${sourceBranch} -> ${targetBranch}] [author: ${userName}] [res]: ${JSON.stringify(res)}`);

    return res;
  }

  async retouchAutoFlow(targetBranch: string, sourceBranch: string) {
    const newTargetBranch = this.getRetouchTargetBranch(targetBranch);
    const iphotoSourceBranch = this.getIPhotoTargetBranch(sourceBranch);
    const mappingName = (name: string) => name.split('/').join('_');
    const midBranch = `p/robot.merge/${mappingName(iphotoSourceBranch)}_to_${mappingName(newTargetBranch)}_${nanoid(6)}`;
    const mrRepos: MrRepos[] = [];
    // 醒图安卓主仓
    mrRepos.push({
      projectId: 64902,
      sourcesBranch: midBranch,
      targetBranch: newTargetBranch,
      isHost: true,
      platform: PlatformType.Android,
    });
    // 醒图安卓子仓
    mrRepos.push({
      projectId: 514925,
      sourcesBranch: midBranch,
      targetBranch: newTargetBranch,
      isHost: false,
      platform: PlatformType.Android,
    });
    mrRepos.push({
      projectId: 134284,
      sourcesBranch: midBranch,
      targetBranch: newTargetBranch,
      isHost: false,
      platform: PlatformType.Android,
    });
    for (const repo of repos.iPhotoSubRepos) {
      const ret = await this.gitlabService.createBranch(repo.projectId, midBranch, iphotoSourceBranch);
      if (ret.code === NetworkCode.Error) {
        throw new Error(ret.message);
      }
    }
    const target_branch = newTargetBranch.split('/');
    const source_branch = iphotoSourceBranch.split('/');
    let target_version = source_branch[source_branch.length - 1];
    if (!isDevelopBranch(newTargetBranch)) {
      target_version = target_branch[targetBranch.length - 1];
    }
    const mrParams = await this.bitsConfigV2Service.buildMrConfig({
      title: `AutoFlow: ${iphotoSourceBranch} -> ${newTargetBranch} [中间分支： ${midBranch}]`,
      repos: mrRepos,
      targetVersion: target_version,
      author: 'zhengbolun.patlon',
      type: MrType.merge,
      customFields: {
        range_of_influence: '纸飞机后台发起MR，和对应操作人进行确认',
        how_to_test_issue: '纸飞机后台发起MR，和对应操作人进行确认',
        the_test_results: 1,
        disable_compress_ld_for_inhouse: true, // iOS 屏蔽压缩链接器
        MR_FORBID_PACKAGE_CHECK: true, // iOS 屏蔽包大小检测
      },
      wip: false,
      review_start_type: 1,
    });

    mrParams.mr_reviewers = [];
    mrParams.review_fetch_mode = 2;

    this.logger.info(
      `raiseMr ${sourceBranch} -> ${targetBranch}] [author: zhengbolun.patlon] [diffParams]: ${JSON.stringify(mrParams)}`,
    );

    const res = await this.bitsService.createMr(mrParams, 'zhengbolun.patlon');
    if (res.code === 0 && res.data) {
      const mrInfo: MrInfo | undefined = await this.bitsService.getMrInfo({
        mrId: res.data.mr_id,
      });
      const card = await this.larkCardService.buildLVUniteAdMrCreateInformCard(
        res.data.mr_id,
        sourceBranch,
        targetBranch,
        [PlatformType.Android],
        mrInfo,
      );
      const userId = await this.larkService.getUserIdByEmail('<EMAIL>');
      if (!userId) {
        this.logger.error(`[Version Release] [sendPociOSIntegrationTestStage] poc user id not found`);
        return res;
      }
      if (userId.user_id) {
        const sendRes = await this.larkService.sendCardMessage(UserIdType.userId, userId.user_id, card);
        this.logger.info(
          `raiseMr ${sourceBranch} -> ${targetBranch}] [author: zhengbolun.patlon] [sendRes]: ${JSON.stringify(sendRes)}`,
        );
      }
      this.logger.info(
        `raiseMr ${sourceBranch} -> ${targetBranch}] [author: zhengbolun.patlon] [res]: ${JSON.stringify(res)}`,
      );
    }
  }

  async sendConflictCardToAuthor(mrId: number, taskKey: string): Promise<void> {
    const mrInfo: MrInfo | undefined = await this.bitsService.getMrInfo({
      mrId,
    });
    if (mrInfo) {
      if (mrInfo.conflicted === ConflictedType.not_conflicted) {
        return;
      }
      const originBranch = this.getBranchInfoByTask(taskKey);
      if (mrInfo.platform.toString() === 'C++') {
        mrInfo.platform = PlatformType.PC;
      }
      let bmInfo = await this.getBMFromBranch(originBranch.origin, mrInfo.platform);
      if (!bmInfo) {
        this.logger.error('getBM error, bmInfo is undefined');
        // 移动端BM兜底
        bmInfo = await this.getBMFromBranch(originBranch.origin, PlatformType.Android);
        if (!bmInfo) {
          this.logger.error('get android BM error, bmInfo is undefined');
          return;
        }
      }
      const rdBM = this.getBM(bmInfo, mrInfo.platform);
      const conflictBlameMap = await this.bitsService.getConflictBlameUrl(mrInfo.project_id, mrInfo.iid);
      if (!conflictBlameMap) {
        this.logger.error('getConflictBlameUrl error');
        return;
      }
      const autoFlowGroupChat = await this.getAutoFlowGroupChatId(taskKey);
      if (!autoFlowGroupChat) {
        this.logger.error('autoFlowGroupChat error, autoFlowGroupChat is undefined');
        return;
      }
      const conflictFiles = await this.downloadAndParseConflictFiles(conflictBlameMap);
      const authorArray = this.getAuthorArray(conflictFiles);
      await this.inviteUserToGroup(authorArray, autoFlowGroupChat);
      this.larkCardService
        .buildMrConflictInformCard(
          mrId,
          mrInfo?.from_branch,
          mrInfo?.target_branch,
          [mrInfo?.platform],
          mrInfo,
          conflictFiles,
          rdBM,
        )
        .then(async (card: Card) => {
          await this.larkService.sendMessage(
            UserIdType.chatId,
            autoFlowGroupChat,
            JSON.stringify(card),
            MessageType.interactive,
          );
        });
    }
  }

  private getAuthorArray(conflictData: Map<string, ConflictFile> | undefined): string[] {
    if (!conflictData || conflictData.size === 0) {
      this.logger.error('conflictData is undefined');
      return [];
    }
    const allAuthor: string[] = [];
    for (const [filename, conflictFile] of conflictData.entries()) {
      conflictFile.conflictBlock.forEach(block => {
        if (block.conflictLine && block.conflictLine.length > 0) {
          let sourceAuthor = 'unknown';
          let targetAuthor = 'unknown';
          if (block.sourceAuthor !== '') {
            sourceAuthor = `${block.sourceAuthor}@bytedance.com`;
          }
          if (block.targetAuthor !== '') {
            targetAuthor = `${block.targetAuthor}@bytedance.com`;
          }
          allAuthor.push(sourceAuthor);
          allAuthor.push(targetAuthor);
        }
      });
    }
    const uniqueAuthors = new Set(allAuthor);
    return Array.from(uniqueAuthors);
  }

  async downloadAndParseConflictFiles(conflictBlameMap: Record<string, string>): Promise<Map<string, ConflictFile>> {
    const result: Map<string, ConflictFile> = new Map();

    for (const filename in conflictBlameMap) {
      if (CONFLICT_IGNORE_FILE.includes(filename)) {
        continue;
      }
      const blameUrl = conflictBlameMap[filename];
      try {
        // 下载文件内容
        const response = await axios.get(blameUrl);
        const fileContent = response.data;

        // 解析冲突行和提交人
        const conflictBlocks = await this.parseConflictLines(fileContent);

        const conflictFile: ConflictFile = {
          filename,
          blameUrl,
          conflictBlock: conflictBlocks,
        };
        result.set(filename, conflictFile);
      } catch (error) {
        this.logger.error(`Failed to download or parse conflict file: ${filename}, Error: ${error}`);
      }
    }

    return result;
  }

  async parseConflictLines(fileContent: string): Promise<ConflictBlock[]> {
    const lines = fileContent.split('\n');
    let insideConflictBlock = false;
    const conflictBlocks: ConflictBlock[] = [];
    let conflictLines: ConflictLineInfo[] = [];
    let insideSourceBlock = false;
    let sourceAuthor = '';
    let targetAuthor = '';

    for (let i = 0; i < lines.length; i++) {
      const line = lines[i];
      if (line.includes('<<<<<<< HEAD')) {
        // 开始一个新的冲突块
        insideConflictBlock = true;
        conflictLines = [];
        insideSourceBlock = true;
        sourceAuthor = '';
        targetAuthor = '';
      } else if (line.includes('>>>>>>>')) {
        // 结束冲突块
        insideConflictBlock = false;
        const uniqueAuthors: <AUTHORS>
        conflictLines.forEach(conflictLineInfo => {
          if (!uniqueAuthors.has(conflictLineInfo.author)) {
            uniqueAuthors.set(conflictLineInfo.author, conflictLineInfo);
          }
        });
        const uniqueConflictLine = Array.from(uniqueAuthors.values());
        const filteredConflictLine = uniqueConflictLine.filter(
          conflict => conflict.author !== 'root' && conflict.author !== 'BitsAdmin',
        );
        conflictBlocks.push({
          sourceAuthor,
          targetAuthor,
          conflictLine: filteredConflictLine,
        });
      } else if (line.includes('=======')) {
        insideSourceBlock = false;
      } else if (insideConflictBlock) {
        const authorPattern = /\(([\w.]+)\s/;
        const authorMatch = line.match(authorPattern);
        if (authorMatch) {
          const author: string = authorMatch[1];
          conflictLines.push({
            lineNumber: i + 1,
            author,
          });
          if (
            insideSourceBlock &&
            !Conflict_IGNORE_AUTHOR.includes(author) &&
            (await this.userExist(`${author}@bytedance.com`))
          ) {
            sourceAuthor = author;
          } else if (!Conflict_IGNORE_AUTHOR.includes(author) && (await this.userExist(`${author}@bytedance.com`))) {
            targetAuthor = author;
          }
        }
      }
    }
    return conflictBlocks;
  }

  async userExist(email: string) {
    const userId = await this.larkService.getUserIdByEmail(email);
    return userId !== undefined;
  }

  private async checkAndroidDiff(repo: ProjectInfo, targetBranch: string, sourceBranch: string): Promise<boolean> {
    const ret = await this.gitlabService.compare(`${repo.projectId}`, targetBranch, sourceBranch, false);

    if (ret.code === NetworkCode.Error) {
      throw new Error(ret.message);
    }

    if (ret.data?.diffs && ret.data?.diffs.length > 0) {
      if (
        (repo === repos.androidMainRepo || repo === repos.androidTTPMainRepo) &&
        ret.data.diffs.length === 1 &&
        ret.data.diffs[0].old_path === 'gradle.properties'
      ) {
        return false;
      }
      return true;
    }
    return false;
  }

  private async checkCommonRepoDiff(targetBranch: string, sourceBranch: string, isOversea: boolean): Promise<boolean> {
    for (const repo of repos.commonSubRepos) {
      let ret;
      if (isDevelopBranch(targetBranch)) {
        const compareBranch = isOversea ? repo.ttpDefaultBranch : repo.defaultBranch;
        ret = await this.gitlabService.compare(`${repo.projectId}`, compareBranch, sourceBranch, false);
      } else {
        ret = await this.gitlabService.compare(`${repo.projectId}`, targetBranch, sourceBranch, false);
      }

      if (ret.code === NetworkCode.Error) {
        throw new Error(ret.message);
      }

      if (ret.data?.diffs && ret.data?.diffs.length > 0) {
        return true;
      }
    }
    return false;
  }

  private async checkBranchExist(branches: VersionBranch[], projectId: number): Promise<VersionBranch[]> {
    const branchPromise = branches.map(branch => this.gitlabService.getBranchInfo(projectId, branch.branch));
    const branchResults = await Promise.all(branchPromise);
    return branchResults
      .filter(result => result.code === 0)
      .map(result => branches.find(value => value.branch === result.data?.name)!);
  }

  private generateBranchFlowInfo(
    branches: VersionBranch[],
    projectInfo: ProjectInfo,
    overseaBranches: VersionBranch[],
    overseaProjectInfo: ProjectInfo,
    platform: PlatformType,
  ): MRBranchInfo[] {
    const result: MRBranchInfo[] = [];
    // 仅最高版本的 release 分支需要向develop 分支合并
    branches.sort((a, b) => a.versionCode - b.versionCode);
    overseaBranches.sort((a, b) => a.versionCode - b.versionCode);
    if (branches.length > 0) {
      const branch = branches[branches.length - 1];
      result.push({
        projectInfo,
        origin: branch.branch,
        target: 'rc/develop',
        platform,
        userName: branch.bmUserName,
      });
    }

    if (overseaBranches.length > 0) {
      const branch = overseaBranches[overseaBranches.length - 1];
      result.push({
        projectInfo: overseaProjectInfo,
        origin: branch.branch,
        target: 'rc/develop',
        platform,
        userName: branch.bmUserName,
      });
    }

    // 老版本的release分支向新版本release分支合并
    for (let i = 0; i < branches.length - 1; i++) {
      for (let j = i + 1; j < branches.length; j++) {
        // const originVersion = branches[i].versionCode;
        // const targetVersion = branches[j].versionCode;
        result.push({
          projectInfo,
          origin: branches[i].branch,
          target: branches[j].branch,
          platform,
          userName: branches[i].bmUserName,
        });
        // // 顺便找找CC的分支
        // const ccOriginBranch = overseaBranches.find(value => value.versionCode === originVersion - 200)?.branch;
        // const bm = overseaBranches.find(value => value.versionCode === originVersion - 200)?.bmUserName;
        // const ccTargetBranch = overseaBranches.find(value => value.versionCode === targetVersion - 200)?.branch;
        // if (ccOriginBranch && bm) {
        //   if (ccTargetBranch) {
        //     result.push({
        //       repoUrl: overseaRepoUrl,
        //       origin: ccOriginBranch,
        //       target: ccTargetBranch,
        //       platform,
        //       userName: bm,
        //     });
        //   } else {
        //     result.push({
        //       repoUrl: overseaRepoUrl,
        //       origin: ccOriginBranch,
        //       target: branches[j].branch,
        //       platform,
        //       userName: bm,
        //     });
        //   }
        // }
        break;
      }
    }

    for (let i = 0; i < overseaBranches.length - 1; i++) {
      for (let j = i + 1; j < overseaBranches.length; j++) {
        result.push({
          projectInfo: overseaProjectInfo,
          origin: overseaBranches[i].branch,
          target: overseaBranches[j].branch,
          platform,
          userName: overseaBranches[i].bmUserName,
        });
        break;
      }
    }
    return result;
  }

  private gitUrl(projectName: string): string {
    return `******************:${projectName}.git`;
  }

  private getUserNameByTask(taskKey: string): string {
    const taskInfo = taskKey.split('-');
    return taskInfo[taskInfo.length - 1];
  }

  private getBranchInfoByTask(taskKey: string): MultiMRBranchInfo {
    const taskInfo = taskKey.split('-');
    return {
      platform: taskInfo[0] as PlatformType,
      origin: taskInfo[1],
      target: taskInfo[2],
      userName: taskInfo[3],
      projectInfo: repos.androidMainRepo,
    };
  }

  private getBM(bmInfo: Record<number, BuildMasterInfo>, platform: PlatformType): string {
    if (platform === PlatformType.Android) {
      const versionBM = bmInfo[BmType.android_version_bm];
      if (versionBM) {
        return versionBM.email.replace('@bytedance.com', '');
      } else {
        return 'tanhaiyang';
      }
    }
    return bmInfo[BmType.rd].email.replace('@bytedance.com', '');
  }

  async getAllPlatformBmInfo(taskKey: string) {
    const allPlatform = [PlatformType.Android, PlatformType.iOS, PlatformType.PC];
    const branch = this.getBranchInfoByTask(taskKey).origin;
    const allBmInfo: BmInfo[] = [];
    for (const platform of allPlatform) {
      const bmInfo = await this.getBMFromBranch(branch, PlatformType[platform as keyof typeof PlatformType]);
      if (!bmInfo) {
        this.logger.error('${this.TAG}: getBMFromBranch error');
        continue;
      }
      const values = Object.values(bmInfo);
      for (const buildMaster of values) {
        allBmInfo.push({
          master_group: 0,
          version: '',
          email: buildMaster.email,
          type: buildMaster.type,
        });
      }
    }
    return allBmInfo;
  }

  async getBMFromBranch(branch: string, platform: PlatformType) {
    let finalPlatform = platform;
    if (platform === PlatformType.PC) {
      // pc的时候需要取versionProcess.pcInfo.bmInfo
      finalPlatform = PlatformType.Android;
    }
    if (isDevelopBranch(branch)) {
      const inDevelopVersions = await this.versionProcessDao.findVersionInDevelop();
      for (const inDevelopVersion of inDevelopVersions) {
        if (isPlatformApp(inDevelopVersion.app_id, platform)) {
          return inDevelopVersion.bmInfo;
        }
        if (platform === PlatformType.PC && inDevelopVersion.pcInfo && inDevelopVersion.pcInfo.bmInfo) {
          return inDevelopVersion.pcInfo.bmInfo;
        }
      }
    }
    const isOverseas = branch.includes('overseas');
    const versionArray = branch.split('/');
    let versionCode = versionArray[versionArray.length - 1];
    if (isOverseas && versionCode.includes('.')) {
      versionCode = versionUtils.cc2lvVersion(versionCode);
    }
    const appId = this.getAppId(finalPlatform);
    const versionProcess = await this.versionProcessDao.getCurrentVersionProcessInfo(appId, versionCode);
    if (versionProcess) {
      if (platform !== PlatformType.PC) {
        return versionProcess.bmInfo;
      } else if (versionProcess.pcInfo && versionProcess.pcInfo.bmInfo) {
        return versionProcess.pcInfo.bmInfo;
      }
    }
    return;
  }

  private getAppId(platform: PlatformType): AppSettingId {
    if (platform === PlatformType.Android) {
      return AppSettingId.LV_ANDROID;
    } else if (platform === PlatformType.iOS) {
      return AppSettingId.LV_IOS;
    } else {
      return AppSettingId.LV_WIN;
    }
  }

  private async buildCreateChatArgs(title: string, ownerId: string, userIds: string[]): Promise<CreateChatArgs> {
    return {
      name: title,
      description: title,
      owner_id: ownerId,
      user_id_list: userIds.filter(value => value),
      bot_id_list: ['cli_9c8628b7b1f1d102'],
    };
  }
  private async getAutoFlowGroupChatId(taskKey: string) {
    const allBmInfo = await this.getAllPlatformBmInfo(taskKey);
    const branch = this.getBranchInfoByTask(taskKey).origin;
    this.logger.info(`${this.TAG} getAutoFlowGroupChatID: branch is ${branch}`);
    const isOverseas = branch.includes('overseas');
    const versionArray = branch.split('/');
    let version = versionArray[versionArray.length - 1];
    if (isOverseas && version.includes('.')) {
      version = versionUtils.cc2lvVersion(version);
    }
    if (!version) {
      this.logger.error('get version error, version is undefined');
      return;
    }
    const versionLegacy = await this.versionModelService.findVersion({
      version,
    } as Version);
    let chatGroupId = versionLegacy.autoFlowChatId;
    if (chatGroupId) {
      return chatGroupId;
    }
    const userSet = new Set<string>();
    for (const bm of allBmInfo) {
      if (bm.type === BmType.rd || bm.type === BmType.android_version_bm) {
        userSet.add(bm.email);
      }
    }
    // 默认拉zhengbolun进回流群
    userSet.add('<EMAIL>');
    userSet.add('<EMAIL>');
    userSet.add('<EMAIL>');
    const allDevelopers = await this.larkService.batchGetUserId(UserIdType.userId, {
      emails: [...userSet],
    });
    const createCharUsers = allDevelopers.map((value: UserData) => value.user_id).filter(value => value);
    const createGroupArgs = await this.buildCreateChatArgs(
      `${version}代码回流群`,
      sample(allDevelopers)!.user_id,
      createCharUsers,
    );
    const createGroupResult = await this.larkService.createLarkGroup(
      {
        user_id_type: UserIdType.userId,
        set_bot_manager: false,
      },
      createGroupArgs,
    );
    if (createGroupResult) {
      chatGroupId = createGroupResult.chat_id;
      versionLegacy.autoFlowChatId = chatGroupId;
      await this.versionModelService.saveVersion(versionLegacy);
    }
    this.logger.info(`createBMGroupChat ${JSON.stringify(versionLegacy)} ${createCharUsers}`);
    return chatGroupId;
  }

  async inviteUserToGroup(emails: string[], chat_id: string) {
    const userInfo = await this.larkService.batchGetUserId(UserIdType.userId, { emails });
    await this.larkService.addUserToChatGroup(
      chat_id,
      UserIdType.userId,
      userInfo.map(v => v.user_id),
    );
  }

  async sendConflictDetectRequest(projectId: string, iid: string, userEmailPrefix: string) {
    const url = `https://optimus.bytedance.net/api/v1/projects/${projectId}/merge_requests/${iid}/`;
    const headers = {
      'Content-Type': 'application/json',
      'Private-Token': 'b3B0aW11c19hZG1pbl9hY2Nlc3NfdG9rZW4=',
      'X-Conty-User': userEmailPrefix,
    };
    const data = {
      username: userEmailPrefix,
      action: 'CONFLICT_DETECT',
    };

    try {
      const response = await axios.post(url, data, { headers });
      this.logger.info(`sendConflictDetectRequest Response: ${response.data}, projectId: ${projectId}, iid: ${iid}`);
      return response.data;
    } catch (error) {
      this.logger.error(`Error occurred: ${error}`);
      return null;
    }
  }
}
